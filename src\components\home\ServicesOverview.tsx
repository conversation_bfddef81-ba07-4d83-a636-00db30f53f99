import Image5 from '../../assets/home/<USER>';
import Image4 from '../../assets/home/<USER>';
import Image3 from '../../assets/home/<USER>';
import Image2 from '../../assets/home/<USER>';
import Image1 from '../../assets/home/<USER>';

function ServicesOverview() {
  const outbreaks = [
    {
      id: 'ebola',
      year: '2014',
      title: 'Ebola',
      image: Image1,
      bgGradient: 'from-red-600 to-orange-700'
    },
    {
      id: 'zika',
      year: '2016',
      title: 'Zika',
      image: Image2,
      bgGradient: 'from-yellow-500 to-green-600'
    },
    {
      id: 'covid',
      year: '2019-20',
      title: 'COVID-19',
      image: Image3,
      bgGradient: 'from-teal-500 to-blue-600'
    },
    {
      id: 'mpox',
      year: '2022',
      title: 'MPOX',
      image: Image4,
      bgGradient: 'from-indigo-500 to-blue-700'
    },
    {
      id: 'orov',
      year: '2024',
      title: 'OROV',
      image: Image5,
      bgGradient: 'from-purple-500 to-pink-600'
    }
  ];

  return (
    <section className=" py-8 sm:py-12 md:py-16 lg:py-20 bg-gray-50 relative overflow-hidden">

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          {/* Header */}
          <div className="mb-8 sm:mb-12 md:mb-16 text-left flex flex-col gap-y-4">
            <h1 className="font-platform text-black font-medium leading-tight tracking-normal text-3xl md:text-4xl lg:text-5xl text-center">
              Leave No Outbreak Unchecked
            </h1>
            <div className="space-y-1 sm:space-y-2 text-center font-inter font-medium">
              <p className="text-lg md:text-xl ">
                From Ebola to COVID-19.
              </p>
              <p className="text-lg md:text-xl">
              International Responder Systems&apos; support services and technology help monitor, evaluate, and even mitigate infectious disease outbreaks across the United States and globally.
              </p>
            </div>
          </div>

          {/* Timeline */}
          <div className="relative">
            {/* Mobile: Stack vertically */}
            <div className="block lg:hidden space-y-4">
              {outbreaks.map((outbreak) => (
                <div
                key={outbreak.id}
                className={`relative overflow-hidden h-48 bg-gradient-to-br ${outbreak.bgGradient} rounded-lg`}
                >
                  <img
                    src={outbreak.image}
                    alt={outbreak.title}
                    className="absolute inset-0 w-full h-full object-cover"
                  />
                  <div className="absolute inset-0 bg-black bg-opacity-20"></div>
                  <div className="relative z-10 p-4 h-full flex flex-col justify-end">
                    <div className="text-white">
                      <div className="text-xl font-bold">{outbreak.year}</div>
                      <div className="text-lg font-semibold">{outbreak.title}</div>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Desktop: Horizontal timeline */}
            <div className="hidden lg:block">
              <div className="relative flex justify-center items-end gap-4 transition-all duration-300 ease-in-out">
                {outbreaks.map((outbreak, index) => {
                  const heights = ['h-[250px]', 'h-[290px]', 'h-[340px]', 'h-[390px]', 'h-[440px]'];
                  return (
                    <div
                    key={outbreak.id}
                    className={`group relative ${heights[index]} shadow-lg w-96 transform hover:scale-105 hover:shadow-xl transition-transform duration-300 ease-in-out cursor-pointer`}
                    >
                      {/* Square behind the card at the top */}
                      <div
                        className={`absolute top-[-14px] rounded-lg left-1/2 transform -translate-x-1/2 w-[220px] h-20 bg-gradient-to-br ${outbreak.bgGradient} rotate-6 opacity-40 group-hover:opacity-100 transition-opacity duration-300 z-0 shadow-md`}
                      ></div>
                      {/* Card */}
                      <div
                        className={`relative bg-gradient-to-br ${outbreak.bgGradient} h-full rounded-lg overflow-hidden shadow-lg z-10`}
                        >
                        <img
                          src={outbreak.image}
                          alt={outbreak.title}
                          className="absolute inset-0 w-full h-full object-cover opacity-70 transition-opacity duration-300 group-hover:opacity-90"
                        />
                        <div className={`relative z-20 p-6 h-full flex flex-col justify-end`}>
                          <div className="text-white text-shadow-md">
                            <div className="text-2xl font-bold mb-2 drop-shadow-lg transition-all duration-300 group-hover:text-opacity-90">
                              {outbreak.year}
                            </div>
                            <div className="text-xl font-semibold drop-shadow-md transition-all duration-300 group-hover:text-opacity-90">
                              {outbreak.title}
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  );
                })}
                
              </div>
            </div>
          </div>
        </div>
    </section>
  );
}

export default ServicesOverview;