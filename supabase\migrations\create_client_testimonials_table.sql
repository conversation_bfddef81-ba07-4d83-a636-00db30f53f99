-- Create client_testimonials table
CREATE TABLE IF NOT EXISTS client_testimonials (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  quote text NOT NULL,
  name text NOT NULL,
  title text NOT NULL,
  organization text NOT NULL,
  organization_logo text,
  profile_image text,
  is_active boolean DEFAULT true,
  display_order integer DEFAULT 0,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Enable RLS
ALTER TABLE client_testimonials ENABLE ROW LEVEL SECURITY;

-- Create policies
CREATE POLICY "Anyone can read active testimonials"
  ON client_testimonials
  FOR SELECT
  USING (is_active = true);

CREATE POLICY "<PERSON><PERSON> can manage testimonials"
  ON client_testimonials
  FOR ALL
  TO authenticated
  USING (EXISTS (
    SELECT 1 FROM profiles
    WHERE id = auth.uid()
    AND role = 'admin'
  ));

-- Create indexes for better performance
CREATE INDEX client_testimonials_created_at_idx ON client_testimonials(created_at);
CREATE INDEX client_testimonials_display_order_idx ON client_testimonials(display_order);
CREATE INDEX client_testimonials_is_active_idx ON client_testimonials(is_active);

-- Create trigger for updated_at
CREATE OR REPLACE FUNCTION update_client_testimonials_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER client_testimonials_updated_at
  BEFORE UPDATE ON client_testimonials
  FOR EACH ROW
  EXECUTE FUNCTION update_client_testimonials_updated_at();

-- Insert sample testimonials (matching the existing ones)
INSERT INTO client_testimonials (quote, name, title, organization, profile_image, display_order) VALUES
  ('GrantReady has revolutionized how we manage our public health grants. We''ve reduced administrative time by 45% and improved our reporting accuracy substantially.', 'Dr. Michael Rodriguez', 'Director of Public Health Programs', 'State Department of Health', '/assets/home/<USER>', 1),
  ('The comprehensive dashboard gives us real-time insights into our grant performance. It''s made coordination between state and local jurisdictions seamless.', 'Dr. Emily Parker', 'Chief Grants Officer', 'County Health Department', '/assets/home/<USER>', 2),
  ('The real-time reporting capabilities have revolutionized our decision-making process. We can now respond to funding opportunities much more efficiently.', 'Dr. Amanda Chen', 'Emergency Preparedness Director', 'Regional Health Authority', '/assets/home/<USER>', 3);
