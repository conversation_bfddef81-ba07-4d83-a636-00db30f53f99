import React, { useState, useRef, useEffect } from 'react';
import { Menu, X, ShoppingCart, UserCircle, ChevronDown, Library, BookOpen, Video, FileText, FilePieChart as FileChart, Calendar, Building2, Newspaper, Users, Briefcase, Package, HelpCircle, MessageSquare, Phone, Mail, Search, Download, ArrowRight, Linkedin } from 'lucide-react';
import { Link, useNavigate } from 'react-router-dom';
import { useAuthStore } from '../store/authStore';
import { useCartStore } from '../store/cartStore';
import { supabase } from '../lib/supabase';
import { useSiteSettings } from '../hooks/useSiteSettings';
import irsLogo from "../assets/images/irs-logo2.jpg";
import FullWidthDropdown from './FullWidthDropdown';
import SolutionsDropdown from './SolutionsDropdown';

export default function Navbar({ site_settings }: { site_settings?: any }) {
  const [isOpen, setIsOpen] = useState(false);
  const [activeDropdown, setActiveDropdown] = useState<string | null>(null);
  const [activeMobileDropdown, setActiveMobileDropdown] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<any[]>([]);
  const [showSearch, setShowSearch] = useState(false);
  const searchRef = useRef<HTMLDivElement>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const user = useAuthStore((state) => state.user);
  const cartItems = useCartStore((state) => state.items);
  const navigate = useNavigate();
  const [role, setRole] = useState<string | null>(null);
  const [profileAvatar, setProfileAvatar] = useState<string | null>(null);
  const { settings } = useSiteSettings();

  const cartItemCount = cartItems.reduce((sum, item) => sum + item.quantity, 0);

  // Helper function to get the correct avatar URL
  const getAvatarUrl = () => {
    // First priority: profile table avatar_url
    if (profileAvatar) {
      return profileAvatar;
    }
    // Second priority: OAuth provider avatar
    if (user?.user_metadata?.avatar_url) {
      return user.user_metadata.avatar_url;
    }
    // No avatar available
    return null;
  };

  useEffect(() => {
    const checkUserProfile = async () => {
      if (user?.id) {
        const { data: profile } = await supabase
          .from('profiles')
          .select('role, avatar_url')
          .eq('id', user.id)
          .single();

        setRole(profile?.role);
        setProfileAvatar(profile?.avatar_url);
      }
    };
    checkUserProfile();

    const handleClickOutside = (event: MouseEvent) => {
      if (searchRef.current && !searchRef.current.contains(event.target as Node)) {
        setShowSearch(false);
        setSearchQuery('');
        setSearchResults([]);
      }
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setActiveDropdown(null);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [user]);

  // Force re-render of cart count when cart items change
  useEffect(() => {
    // This effect ensures the cart count is always up-to-date
  }, [cartItems]);

  const handleSearch = async (query: string) => {
    setSearchQuery(query);
    if (query.length < 2) {
      setSearchResults([]);
      return;
    }

    try {
      // Search in pages
      const { data: pages } = await supabase
        .from('pages')
        .select('title, slug')
        .ilike('title', `%${query}%`)
        .limit(3);

      // Search in blog posts
      const { data: posts } = await supabase
        .from('blog_posts')
        .select('title, id')
        .ilike('title', `%${query}%`)
        .eq('status', 'published')
        .limit(3);

      // Search in products
      const { data: products } = await supabase
        .from('products')
        .select('name, id')
        .ilike('name', `%${query}%`)
        .limit(3);

      const { data: teams } = await supabase
        .from('teams')
        .select('name, id, role')
        .or(`name.ilike.%${query}%,role.ilike.%${query}%`)
        .limit(3);

      setSearchResults([
        ...(pages || []).map(page => ({ ...page, type: 'page' })),
        ...(posts || []).map(post => ({ ...post, type: 'post' })),
        ...(products || []).map(product => ({ ...product, type: 'product' })),
        ...(teams || []).map(team => ({ ...team, type: 'team' }))
      ]);
    } catch (error) {
      console.error('Search error:', error);
    }
  };

  const handleSearchItemClick = (item: any) => {
    setShowSearch(false);
    setSearchQuery('');
    setSearchResults([]);

    switch (item.type) {
      case 'page':
        navigate(`/${item.slug}`);
        break;
      case 'post':
        navigate(`/blog`);
        break;
      case 'product':
        navigate(`/products`);
        break;
      case 'team':
        navigate(`/leadership`);
        break;
    }
  };

  const toggleDropdown = (dropdown: string) => {
    setActiveDropdown(activeDropdown === dropdown ? null : dropdown);
  };

  const toggleMobileDropdown = (dropdown: string) => {
    setActiveMobileDropdown(activeMobileDropdown === dropdown ? null : dropdown);
  };

  const solutionsLinks = [
    { name: 'Overview', path: '/solutions' },
    // { name: 'Products', path: '/products' },
    { name: 'GrantReady™', path: '/solutions/grantready' },
    { name: 'SOAR', path: '/solutions/soar' },
    { name: 'ELENOR', path: '/solutions/elenor' }
  ];

  const CustomIconBlack = () => (
    <svg width="33" height="32" viewBox="0 0 33 32" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M6.98828 26.3691C8.48205 26.3693 9.69326 27.5804 9.69336 29.0742C9.69336 30.5681 8.48211 31.7792 6.98828 31.7793C5.49433 31.7793 4.2832 30.5682 4.2832 29.0742C4.2833 27.5804 5.4944 26.3691 6.98828 26.3691ZM21.6406 24.1152C23.1344 24.1154 24.3457 25.3265 24.3457 26.8203C24.3456 28.3141 23.1344 29.5252 21.6406 29.5254C20.1467 29.5254 18.9356 28.3142 18.9355 26.8203C18.9355 25.3264 20.1467 24.1152 21.6406 24.1152ZM18.0547 28.165L10.7256 29.0215L10.5947 27.9023L17.9238 27.0459L18.0547 28.165ZM16.5146 18.8174L9.96582 26.4209L9.11133 25.6855L15.6611 18.082L16.5146 18.8174ZM6.2373 25.1777L5.14746 25.4619L2.93066 16.9609L4.02148 16.6768L6.2373 25.1777ZM21.1221 23.252L20.0449 23.5811L18.7959 19.4854L19.874 19.1562L21.1221 23.252ZM18.4844 13.2949C19.9783 13.2949 21.1894 14.5061 21.1895 16C21.1895 17.4939 19.9783 18.7051 18.4844 18.7051C16.9905 18.705 15.7793 17.4939 15.7793 16C15.7793 14.5061 16.9905 13.295 18.4844 13.2949ZM29.7549 13.2949C31.2488 13.2949 32.4599 14.5061 32.46 16C32.46 17.4939 31.2488 18.7051 29.7549 18.7051C28.2612 18.7048 27.0508 17.4938 27.0508 16C27.0508 14.5062 28.2612 13.2952 29.7549 13.2949ZM26.1484 16.6768H22.3164V15.5488H26.1484V16.6768ZM2.70508 10.1396C4.19886 10.1396 5.40989 11.35 5.41016 12.8438C5.41016 14.3377 4.19902 15.5488 2.70508 15.5488C1.21114 15.5488 0 14.3377 0 12.8438C0.000261786 11.35 1.2113 10.1397 2.70508 10.1396ZM24.7305 8.77051L21.4268 13.3408L20.5127 12.6797L23.8174 8.11035L24.7305 8.77051ZM17.4336 12.6807L16.4199 13.1729L13.0635 6.26074L14.0771 5.76855L17.4336 12.6807ZM9.55859 5.75488L5.30176 9.97559L4.50879 9.1748L8.76562 4.9541L9.55859 5.75488ZM26.5996 2.7002C28.0935 2.70024 29.3046 3.91141 29.3047 5.40527C29.3047 6.89919 28.0935 8.11031 26.5996 8.11035C25.1057 8.11035 23.8945 6.89922 23.8945 5.40527C23.8946 3.91138 25.1057 2.7002 26.5996 2.7002ZM12.1729 0.220703C13.6667 0.220776 14.8779 1.43188 14.8779 2.92578C14.8779 4.41966 13.6667 5.63079 12.1729 5.63086C10.6789 5.63086 9.4678 4.41971 9.46777 2.92578C9.46777 1.43183 10.6789 0.220703 12.1729 0.220703ZM23.1016 3.35645L22.8584 4.45703L15.6533 2.86426L15.8965 1.76367L23.1016 3.35645Z" fill="black" />
    </svg>
  );

  const CustomIcon = () => (
    <svg width="33" height="32" viewBox="0 0 33 32" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M6.98828 26.3691C8.48205 26.3693 9.69326 27.5804 9.69336 29.0742C9.69336 30.5681 8.48211 31.7792 6.98828 31.7793C5.49433 31.7793 4.2832 30.5682 4.2832 29.0742C4.2833 27.5804 5.4944 26.3691 6.98828 26.3691ZM21.6406 24.1152C23.1344 24.1154 24.3457 25.3265 24.3457 26.8203C24.3456 28.3141 23.1344 29.5252 21.6406 29.5254C20.1467 29.5254 18.9356 28.3142 18.9355 26.8203C18.9355 25.3264 20.1467 24.1152 21.6406 24.1152ZM18.0547 28.165L10.7256 29.0215L10.5947 27.9023L17.9238 27.0459L18.0547 28.165ZM16.5146 18.8174L9.96582 26.4209L9.11133 25.6855L15.6611 18.082L16.5146 18.8174ZM6.2373 25.1777L5.14746 25.4619L2.93066 16.9609L4.02148 16.6768L6.2373 25.1777ZM21.1221 23.252L20.0449 23.5811L18.7959 19.4854L19.874 19.1562L21.1221 23.252ZM18.4844 13.2949C19.9783 13.2949 21.1894 14.5061 21.1895 16C21.1895 17.4939 19.9783 18.7051 18.4844 18.7051C16.9905 18.705 15.7793 17.4939 15.7793 16C15.7793 14.5061 16.9905 13.295 18.4844 13.2949ZM29.7549 13.2949C31.2488 13.2949 32.4599 14.5061 32.46 16C32.46 17.4939 31.2488 18.7051 29.7549 18.7051C28.2612 18.7048 27.0508 17.4938 27.0508 16C27.0508 14.5062 28.2612 13.2952 29.7549 13.2949ZM26.1484 16.6768H22.3164V15.5488H26.1484V16.6768ZM2.70508 10.1396C4.19886 10.1396 5.40989 11.35 5.41016 12.8438C5.41016 14.3377 4.19902 15.5488 2.70508 15.5488C1.21114 15.5488 0 14.3377 0 12.8438C0.000261786 11.35 1.2113 10.1397 2.70508 10.1396ZM24.7305 8.77051L21.4268 13.3408L20.5127 12.6797L23.8174 8.11035L24.7305 8.77051ZM17.4336 12.6807L16.4199 13.1729L13.0635 6.26074L14.0771 5.76855L17.4336 12.6807ZM9.55859 5.75488L5.30176 9.97559L4.50879 9.1748L8.76562 4.9541L9.55859 5.75488ZM26.5996 2.7002C28.0935 2.70024 29.3046 3.91141 29.3047 5.40527C29.3047 6.89919 28.0935 8.11031 26.5996 8.11035C25.1057 8.11035 23.8945 6.89922 23.8945 5.40527C23.8946 3.91138 25.1057 2.7002 26.5996 2.7002ZM12.1729 0.220703C13.6667 0.220776 14.8779 1.43188 14.8779 2.92578C14.8779 4.41966 13.6667 5.63079 12.1729 5.63086C10.6789 5.63086 9.4678 4.41971 9.46777 2.92578C9.46777 1.43183 10.6789 0.220703 12.1729 0.220703ZM23.1016 3.35645L22.8584 4.45703L15.6533 2.86426L15.8965 1.76367L23.1016 3.35645Z" fill="white" />
    </svg>
  );

  const TwitterIcon = () => (
    <svg viewBox="0 0 22 22" aria-hidden="true" className="h-4 w-4">
      <g>
        <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z" fill="currentColor"></path>
      </g>
    </svg>
  );

  const FacebookIcon = () => (
    <svg viewBox="0 0 24 24" aria-hidden="true" className="h-4 w-4">
      <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z" fill="currentColor" />
    </svg>
  );

  const solutionsCards = [
    {
      name: 'Our Solutions',
      path: '/solutions',
      description: 'Explore all our innovative solutions in one place.',
      icon: CustomIconBlack,
      bgColor: 'white',
      textColor: 'text-black'
    },
    {
      name: 'GrantReady™',
      path: '/solutions/grantready',
      description: 'Streamline your grant management process with our comprehensive solution.',
      icon: CustomIcon,
      bgColor: 'radial-gradient(50% 55% at 0% 0%, #41AC90 0%, #7681E6 100%)',
      textColor: 'text-white'
    },
    {
      name: 'SOAR',
      path: '/solutions/soar',
      description: 'STLT Outbreak, Analytics and Response tailored for public health providers.',
      icon: CustomIcon,
      bgColor: 'radial-gradient(50% 55% at 0% 0%, #F3AE7A 0%, #EE8149 100%)',
      textColor: 'text-white'
    },
    {
      name: 'ELENOR',
      path: '/solutions/elenor',
      description: 'Advanced emergency response and coordination platform.',
      icon: CustomIcon,
      bgColor: 'radial-gradient(50% 55% at 0% 0%, #3082F5 0%, #313791 100%)', // updated to new blue gradient
      textColor: 'text-white'
    }
  ];

  const resourcesLinks = [
    ...(site_settings?.features.resourcesLibraryEnabled ? [{ icon: Library, name: 'Resources Library', path: '/resources' }] : []),
    ...(site_settings?.features.blogEnabled ? [{ icon: BookOpen, name: 'Blog', path: '/blog' }] : []),
    ...(site_settings?.features.webinarsEnabled ? [{ icon: Video, name: 'Webinars', path: '/webinars' }] : []),
    ...(site_settings?.features.whitepapersEnabled ? [{ icon: Download, name: 'Whitepapers', path: '/whitepapers' }] : []),
    ...(site_settings?.features.guidesEnabled ? [{ icon: FileText, name: 'Guides', path: '/guides' }] : []),
    ...(site_settings?.features.caseStudiesEnabled ? [{ icon: FileChart, name: 'Case Studies', path: '/case-studies' }] : []),
    ...(site_settings?.features.eventsEnabled ? [{ icon: Calendar, name: 'Events', path: '/events' }] : [])
  ];

  const resourcesCards = [
    ...(site_settings?.features.resourcesLibraryEnabled ? [{
      name: 'Resources Library',
      path: '/resources',
      description: 'Access our comprehensive collection of resources and documentation.',
      icon: Library,
      bgColor: 'radial-gradient(50% 55% at 0% 0%, #3B82F6 0%, #6366F1 100%)', // blue
      textColor: 'text-white'
    }] : []),
    ...(site_settings?.features.blogEnabled ? [{
      name: 'Blog',
      path: '/blog',
      description: 'Stay updated with the latest insights, trends, and industry news.',
      icon: BookOpen,
      bgColor: 'radial-gradient(50% 55% at 0% 0%, #10B981 0%, #059669 100%)', // green
      textColor: 'text-white'
    }] : []),
    ...(site_settings?.features.webinarsEnabled ? [{
      name: 'Webinars',
      path: '/webinars',
      description: 'Join our educational webinars and training sessions.',
      icon: Video,
      bgColor: 'radial-gradient(50% 55% at 0% 0%, #F59E0B 0%, #D97706 100%)', // orange
      textColor: 'text-white'
    }] : []),
    ...(site_settings?.features.whitepapersEnabled ? [{
      name: 'Whitepapers',
      path: '/whitepapers',
      description: 'Download detailed research papers and technical documentation.',
      icon: Download,
      bgColor: 'radial-gradient(50% 55% at 0% 0%, #06B6D4 0%, #3B82F6 100%)', // teal to blue
      textColor: 'text-white'
    }] : []),
    ...(site_settings?.features.guidesEnabled ? [{
      name: 'Guides',
      path: '/guides',
      description: 'Step-by-step guides to help you get the most out of our solutions.',
      icon: FileText,
      bgColor: 'radial-gradient(50% 55% at 0% 0%, #6366F1 0%, #7C3AED 100%)', // indigo
      textColor: 'text-white'
    }] : []),
    ...(site_settings?.features.caseStudiesEnabled ? [{
      name: 'Case Studies',
      path: '/case-studies',
      description: 'Real-world examples of successful implementations and outcomes.',
      icon: FileChart,
      bgColor: 'radial-gradient(50% 55% at 0% 0%, #06B6D4 0%, #0891B2 100%)', // cyan
      textColor: 'text-white'
    }] : []),
    ...(site_settings?.features.eventsEnabled ? [{
      name: 'Events',
      path: '/events',
      description: 'Upcoming events and conferences related to our industry.',
      icon: Calendar,
      bgColor: 'radial-gradient(50% 55% at 0% 0%, #F59E0B 0%, #D97706 100%)', // yellow to orange
      textColor: 'text-white'
    }] : [])
  ];

  const companyLinks = [
    { icon: Building2, name: 'About Us', path: '/about' },
    { icon: Users, name: 'Leadership Team', path: '/leadership' },
    { icon: Newspaper, name: 'News', path: '/news' },
    { icon: Briefcase, name: 'Careers', path: '/careers' }
  ];

  const companyCards = [
    {
      name: 'About Us',
      path: '/about',
      description: 'Learn about our mission, vision, and commitment to excellence.',
      icon: Building2,
      bgColor: 'radial-gradient(50% 55% at 0% 0%, #06B6D4 0%, #10B981 100%)', // teal to green
      textColor: 'text-white'
    },
    {
      name: 'Leadership Team',
      path: '/leadership',
      description: 'Meet the experienced professionals leading our organization.',
      icon: Users,
      bgColor: 'radial-gradient(50% 55% at 0% 0%, #06B6D4 0%, #72b0fb 100%)', // teal to soft blue
      textColor: 'text-white'
    },
    {
      name: 'News',
      path: '/news',
      description: 'Stay informed with our latest company announcements and updates.',
      icon: Newspaper,
      bgColor: 'radial-gradient(50% 55% at 0% 0%, #F59E0B 0%, #D97706 100%)', // orange
      textColor: 'text-white'
    },
    {
      name: 'Careers',
      path: '/careers',
      description: 'Join our team and help shape the future of emergency response.',
      icon: Briefcase,
      bgColor: 'radial-gradient(50% 55% at 0% 0%, #06B6D4 0%, #0891B2 100%)', // cyan
      textColor: 'text-white'
    }
  ];

  const supportLinks = [
    { icon: MessageSquare, name: 'Support Center', path: '/support' },
    { icon: HelpCircle, name: 'FAQ', path: '/faq' },
    { icon: Phone, name: 'Contact Sales', path: '/contact' },
    { icon: Mail, name: 'Email Support', href: 'mailto:<EMAIL>' }
  ];

  const supportCards = [
    {
      name: 'Support Center',
      path: '/support',
      description: 'Get help with technical issues and product support.',
      icon: MessageSquare,
      bgColor: 'radial-gradient(50% 55% at 0% 0%, #2563EB 0%, #1D4ED8 100%)',
      textColor: 'text-white'
    },
    {
      name: 'FAQ',
      path: '/faq',
      description: 'Find answers to frequently asked questions.',
      icon: HelpCircle,
      bgColor: 'radial-gradient(50% 55% at 0% 0%, #7C3AED 0%, #6D28D9 100%)',
      textColor: 'text-white'
    },
    {
      name: 'Contact Sales',
      path: '/contact',
      description: 'Connect with our sales team for product inquiries.',
      icon: Phone,
      bgColor: 'radial-gradient(50% 55% at 0% 0%, #059669 0%, #047857 100%)',
      textColor: 'text-white'
    },
    {
      name: 'Email Support',
      href: 'mailto:<EMAIL>',
      description: 'Send us an email for direct support assistance.',
      icon: Mail,
      bgColor: 'radial-gradient(50% 55% at 0% 0%, #DC2626 0%, #B91C1C 100%)',
      textColor: 'text-white'
    }
  ];

  return (
    <nav className="bg-white sticky top-1 w-[99%] max-w-[1900px] border-2 shadow-md overflow-hidden mx-auto z-50 font-inter rounded-lg">
      {/* Desktop Layout with Logo Spanning Both Rows */}
      <div className="hidden lg:block">
        <div className="w-full mx-auto">
          {/* Top Bar Content */}
          <div className="col-span-12 bg-gray-50 border-b border-gray-200 rounded-tr-lg px-2 h-full">
            <div className="relative flex items-center justify-between h-10 text-sm">
              {/* Left Side - Email */}
              <div className="flex items-center text-gray-600">
                <Mail className="h-4 w-4 mr-2" />
                <span>{settings?.general?.contactEmail || '<EMAIL>'}</span>
              </div>

              {/* Center - Social Media Icons - Absolutely positioned for perfect centering */}
              <div className="absolute left-1/2 transform -translate-x-1/2 flex items-center justify-center space-x-3">
                {settings?.general?.socialLinks?.linkedin && (
                  <a
                    href={settings.general.socialLinks.linkedin}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-gray-600 hover:text-[#C42E0E] transition-colors duration-200"
                    aria-label="LinkedIn"
                  >
                    <Linkedin className="h-4 w-4" />
                  </a>
                )}
                {settings?.general?.socialLinks?.twitter && (
                  <a
                    href={settings.general.socialLinks.twitter}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-gray-600 hover:text-[#C42E0E] transition-colors duration-200"
                    aria-label="Twitter/X"
                  >
                    <TwitterIcon />
                  </a>
                )}
                {settings?.general?.socialLinks?.facebook && (
                  <a
                    href={settings.general.socialLinks.facebook}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-gray-600 hover:text-[#C42E0E] transition-colors duration-200"
                    aria-label="Facebook"
                  >
                    <FacebookIcon />
                  </a>
                )}
              </div>

              {/* Right Side - User Actions */}
              <div className="flex items-center space-x-6 w-fit">
                {role === 'admin' && (
                  <Link to="/admin/dashboard" className="text-gray-600 hover:text-[#C42E0E] transition-colors duration-200">
                    Admin Dashboard
                  </Link>
                )}
                {user ? (
                  <Link to="/profile" className="flex items-center text-gray-600 hover:text-[#C42E0E] transition-colors duration-200">
                    {getAvatarUrl() ? (
                      <img src={getAvatarUrl()!} alt="User Avatar" className="h-6 w-6 rounded-full border mr-2" />
                    ) : (
                      <UserCircle className="h-5 w-5 mr-2" />
                    )}
                    My Account
                  </Link>
                ) : (
                  <Link to="/login" className="text-gray-600 hover:text-[#C42E0E] transition-colors duration-200">
                    Sign In / Register
                  </Link>
                )}
              </div>
            </div>
          </div>
          {/* Main header row (logo + nav) */}
          <div id='main-header' className="flex flex-row items-center justify-between w-full px-0 pt-1 pb-1.5">
            <div className="flex items-center justify-center pl-1 w-fit">
              <Link to="/" className="flex items-center shrink-0">
                <img
                  src={irsLogo}
                  alt="International Responder Systems"
                  className="object-contain rounded-md w-[350px]"
                />
              </Link>
            </div>

            {/* Main Navigation */}
            <div className="flex items-center justify-center w-fit space-x-8">
              <Link
                to="/"
                className="text-[#131D47] hover:text-[#C42E0E] hover:bg-gray-100 px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200 flex items-center h-full"
              >
                Home
              </Link>

              {/* Solutions Dropdown */}
              <div
                className="relative group"
                onMouseEnter={() => setActiveDropdown('solutions')}
                onMouseLeave={() => setActiveDropdown(null)}
              >
                <button
                  onClick={() => toggleDropdown('solutions')}
                  className={`flex items-center text-[#131D47] hover:text-[#C42E0E] hover:bg-gray-100 px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200 ${activeDropdown === 'solutions' ? 'text-[#C42E0E]' : ''}`}
                >
                  Solutions
                  <ChevronDown className={`ml-1 h-4 w-4 transform transition-transform duration-200 ${activeDropdown === 'solutions' ? 'rotate-180' : ''}`} />
                </button>

                <SolutionsDropdown
                  cards={solutionsCards}
                  isActive={activeDropdown === 'solutions'}
                  onClose={() => setActiveDropdown(null)}
                />
              </div>

              {/* Resources Dropdown - Only show if there are enabled resource items */}
              {resourcesCards.length > 0 && (
                <div className="relative group">
                  <button
                    onClick={() => toggleDropdown('resources')}
                    className={`flex items-center text-[#131D47] hover:text-[#C42E0E] hover:bg-gray-100 px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200 ${activeDropdown === 'resources' ? 'text-[#C42E0E]' : ''}`}
                  >
                    Resources
                    <ChevronDown className={`ml-1 h-4 w-4 transform transition-transform duration-200 ${activeDropdown === 'resources' ? 'rotate-180' : ''}`} />
                  </button>

                  <FullWidthDropdown
                    cards={resourcesCards}
                    isActive={activeDropdown === 'resources'}
                    activeDropdown={activeDropdown}
                    onClose={() => setActiveDropdown(null)}
                  />
                </div>
              )}

              {/* Company Dropdown */}
              <div className="relative group">
                <button
                  onClick={() => toggleDropdown('company')}
                  className={`flex items-center text-[#131D47] hover:text-[#C42E0E] hover:bg-gray-100 px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200 ${activeDropdown === 'company' ? 'text-[#C42E0E]' : ''}`}
                >
                  Company
                  <ChevronDown className={`ml-1 h-4 w-4 transform transition-transform duration-200 ${activeDropdown === 'company' ? 'rotate-180' : ''}`} />
                </button>

                <FullWidthDropdown
                  cards={companyCards}
                  isActive={activeDropdown === 'company'}
                  activeDropdown={activeDropdown}
                  onClose={() => setActiveDropdown(null)}
                />
              </div>

              {/* Support Dropdown */}
              <div className="relative group">
                <button
                  onClick={() => toggleDropdown('support')}
                  className={`flex items-center text-[#131D47] hover:text-[#C42E0E] hover:bg-gray-100 px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200 ${activeDropdown === 'support' ? 'text-[#C42E0E]' : ''}`}
                >
                  Support
                  <ChevronDown className={`ml-1 h-4 w-4 transform transition-transform duration-200 ${activeDropdown === 'support' ? 'rotate-180' : ''}`} />
                </button>

                <FullWidthDropdown
                  cards={supportCards}
                  isActive={activeDropdown === 'support'}
                  activeDropdown={activeDropdown}
                  onClose={() => setActiveDropdown(null)}
                />
              </div>
            </div>
            {/* Main Header Content */}
            {/* <div id='main-header-child2' className="flex flex-1 items-center justify-end gap-x-5 bg-blue-400 px-2"> */}

            {/* Action Buttons */}
            <div className="hidden lg:flex items-center space-x-6 px-2">
              {/* Utility Actions */}
              <div className="flex items-center space-x-3 h-full">
                <button
                  onClick={() => setShowSearch(!showSearch)}
                  className="text-[#131D47] hover:text-[#C42E0E] p-2 rounded-lg hover:bg-gray-50 transition-all duration-200"
                  aria-label="Search"
                >
                  <Search className="h-5 w-5" />
                </button>

                <Link
                  to="/cart"
                  className="relative text-[#131D47] hover:text-[#C42E0E] p-2 rounded-lg hover:bg-gray-50 transition-all duration-200"
                  aria-label="Shopping cart"
                >
                  <ShoppingCart className="h-5 w-5" />
                  {cartItemCount > 0 && (
                    <span className="absolute -top-1 -right-1 bg-[#C42E0E] text-white text-xs w-4 h-4 rounded-full flex items-center justify-center font-medium">
                      {cartItemCount}
                    </span>
                  )}
                </Link>
              </div>

              {/* Book a Demo Button - Primary CTA at far right */}
              <Link
                to="/book-a-demo"
                className="flex items-center bg-black text-white pl-4 pr-1 py-1.5 rounded-[4.73px] hover:text-white transition-all duration-200 group shadow-lg justify-center">
                <span className="mr-2 font-semibold text-sm capitalize">Book a Demo</span>
                <div className="bg-white rounded-[4.73px] p-1 group-hover:translate-x-0.5 transition-transform duration-200 flex items-center justify-center">
                  <ArrowRight className="h-4 w-4 text-black" />
                </div>
              </Link>
            </div>
            {/* </div> */}
          </div>
        </div>
      </div>

      {/* Mobile Layout */}
      <div className="lg:hidden">
        <div className="max-w-7xl mx-auto px-4 sm:px-6">
          <div className="flex justify-between items-center h-16 sm:h-18">
            <div className="flex items-center">
              <Link to="/" className="flex items-center">
                <img
                  src={irsLogo}
                  alt="International Responder Systems"
                  className="w-48 object-contain"
                />
              </Link>
            </div>

            <div className="flex items-center space-x-2 sm:space-x-3">
              <Link
                to="/cart"
                className="relative text-[#131D47] hover:text-[#C42E0E] transition-colors duration-200 p-2"
                aria-label="Shopping cart"
              >
                <ShoppingCart className="h-5 w-5" />
                {cartItemCount > 0 && (
                  <span className="absolute -top-1 -right-1 bg-[#C42E0E] text-white text-xs w-4 h-4 rounded-full flex items-center justify-center text-[10px]">
                    {cartItemCount}
                  </span>
                )}
              </Link>

              {user && (
                <Link
                  to="/profile"
                  className="text-[#131D47] hover:text-[#C42E0E] transition-colors duration-200 p-2"
                  aria-label="User profile"
                >
                  {getAvatarUrl() ? (
                    <img
                      src={getAvatarUrl()!}
                      alt="User Avatar"
                      className="h-5 w-5 rounded-full border border-gray-200"
                    />
                  ) : (
                    <UserCircle className="h-5 w-5" />
                  )}
                </Link>
              )}

              <button
                onClick={() => setIsOpen(!isOpen)}
                className="inline-flex items-center justify-center p-2 rounded-md text-[#131D47] hover:text-[#C42E0E] focus:outline-none transition-colors duration-200"
                aria-label="Main menu"
                aria-expanded={isOpen}
              >
                {isOpen ? (
                  <X className="h-6 w-6" aria-hidden="true" />
                ) : (
                  <Menu className="h-6 w-6" aria-hidden="true" />
                )}
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Search Overlay */}
      {showSearch && (
        <div
          ref={searchRef}
          className="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm transition-opacity duration-300 ease-in-out z-50"
          style={{ top: '7.5rem' }} // Match new navbar height with utility bar
        >
          <div className="max-w-3xl mx-auto p-4 animate-fade-in-down">
            <div className="relative bg-white rounded-lg overflow-hidden shadow-2xl">
              <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
              <input
                type="text"
                placeholder="Search everything..."
                value={searchQuery}
                onChange={(e) => handleSearch(e.target.value)}
                className="w-full pl-12 pr-12 py-4 text-lg border-0 focus:ring-2 focus:ring-[#C42E0E] focus:border-transparent"
                autoFocus
              />
              <button
                onClick={() => setShowSearch(false)}
                className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors"
                aria-label="Close search"
              >
                <X className="h-5 w-5" />
              </button>
            </div>

            {searchResults.length > 0 && (
              <div className="mt-2 bg-white rounded-lg shadow-lg overflow-hidden">
                {searchResults.map((result, index) => (
                  <button
                    key={index}
                    onClick={() => handleSearchItemClick(result)}
                    className="w-full px-4 py-3 text-left hover:bg-gray-50 flex items-center transition-colors duration-150"
                  >
                    {result.type === 'page' && <FileText className="h-5 w-5 text-gray-400 mr-3" />}
                    {result.type === 'post' && <BookOpen className="h-5 w-5 text-gray-400 mr-3" />}
                    {result.type === 'product' && <Package className="h-5 w-5 text-gray-400 mr-3" />}
                    {result.type === 'team' && <Users className="h-5 w-5 text-gray-400 mr-3" />}
                    <div>
                      <div className="font-medium text-gray-800">{result.title || result.name}</div>
                      <div className="text-sm text-gray-500 capitalize">{result.type}</div>
                    </div>
                  </button>
                ))}
              </div>
            )}

            {searchQuery.length >= 2 && searchResults.length === 0 && (
              <div className="mt-4 bg-white rounded-lg shadow-lg p-4 text-center text-gray-500">
                No results found for "{searchQuery}"
              </div>
            )}
          </div>
        </div>
      )}

      {/* Mobile Menu */}
      {isOpen && (
        <div className="lg:hidden bg-white shadow-lg">
          <div className="px-3 pt-2 pb-3 space-y-1 sm:px-4 max-h-[calc(100vh-64px)] sm:max-h-[calc(100vh-72px)] lg:max-h-[calc(100vh-80px)] overflow-y-auto">
            <Link
              to="/"
              className="block text-[#131D47] hover:text-[#C42E0E] px-3 py-2 rounded-md text-base font-medium transition-colors duration-200"
              onClick={() => setIsOpen(false)}
            >
              Home
            </Link>

            {/* Mobile Solutions Dropdown */}
            <div className="py-1">
              <button
                onClick={() => toggleMobileDropdown('solutions')}
                className="flex items-center justify-between w-full text-[#131D47] hover:text-[#C42E0E] px-3 py-2 rounded-md text-base font-medium transition-colors duration-200"
              >
                <span>Solutions</span>
                <ChevronDown className={`h-4 w-4 transform transition-transform duration-200 ${activeMobileDropdown === 'solutions' ? 'rotate-180' : ''}`} />
              </button>
              {activeMobileDropdown === 'solutions' && (
                <div className="pl-4 space-y-1">
                  {solutionsLinks.map((item) => (
                    <Link
                      key={item.path}
                      to={item.path}
                      className="block text-gray-600 hover:text-[#C42E0E] px-3 py-2 rounded-md text-sm transition-colors duration-200"
                      onClick={() => setIsOpen(false)}
                    >
                      {item.name}
                    </Link>
                  ))}
                </div>
              )}
            </div>

            {/* Mobile Resources Dropdown - Only show if there are enabled resource items */}
            {resourcesLinks.length > 0 && (
              <div className="py-1">
                <button
                  onClick={() => toggleMobileDropdown('resources')}
                  className="flex items-center justify-between w-full text-[#131D47] hover:text-[#C42E0E] px-3 py-2 rounded-md text-base font-medium transition-colors duration-200"
                >
                  <span>Resources</span>
                  <ChevronDown className={`h-4 w-4 transform transition-transform duration-200 ${activeMobileDropdown === 'resources' ? 'rotate-180' : ''}`} />
                </button>
                {activeMobileDropdown === 'resources' && (
                  <div className="pl-4 space-y-1">
                    {resourcesLinks.map((item) => (
                      <Link
                        key={item.path}
                        to={item.path}
                        className="flex items-center text-gray-600 hover:text-[#C42E0E] px-3 py-2 rounded-md text-sm transition-colors duration-200"
                        onClick={() => setIsOpen(false)}
                      >
                        <item.icon className="h-4 w-4 mr-2 text-gray-400" />
                        {item.name}
                      </Link>
                    ))}
                  </div>
                )}
              </div>
            )}

            {/* Mobile Company Dropdown */}
            <div className="py-1">
              <button
                onClick={() => toggleMobileDropdown('company')}
                className="flex items-center justify-between w-full text-[#131D47] hover:text-[#C42E0E] px-3 py-2 rounded-md text-base font-medium transition-colors duration-200"
              >
                <span>Company</span>
                <ChevronDown className={`h-4 w-4 transform transition-transform duration-200 ${activeMobileDropdown === 'company' ? 'rotate-180' : ''}`} />
              </button>
              {activeMobileDropdown === 'company' && (
                <div className="pl-4 space-y-1">
                  {companyLinks.map((item) => (
                    <Link
                      key={item.path}
                      to={item.path}
                      className="flex items-center text-gray-600 hover:text-[#C42E0E] px-3 py-2 rounded-md text-sm transition-colors duration-200"
                      onClick={() => setIsOpen(false)}
                    >
                      <item.icon className="h-4 w-4 mr-2 text-gray-400" />
                      {item.name}
                    </Link>
                  ))}
                </div>
              )}
            </div>

            {/* Mobile Support Dropdown */}
            <div className="py-1">
              <button
                onClick={() => toggleMobileDropdown('support')}
                className="flex items-center justify-between w-full text-[#131D47] hover:text-[#C42E0E] px-3 py-2 rounded-md text-base font-medium transition-colors duration-200"
              >
                <span>Support</span>
                <ChevronDown className={`h-4 w-4 transform transition-transform duration-200 ${activeMobileDropdown === 'support' ? 'rotate-180' : ''}`} />
              </button>
              {activeMobileDropdown === 'support' && (
                <div className="pl-4 space-y-1">
                  {supportLinks.map((item) => (
                    item.href ? (
                      <a
                        key={item.href}
                        href={item.href}
                        className="flex items-center text-gray-600 hover:text-[#C42E0E] px-3 py-2 rounded-md text-sm transition-colors duration-200"
                      >
                        <item.icon className="h-4 w-4 mr-2 text-gray-400" />
                        {item.name}
                      </a>
                    ) : (
                      <Link
                        key={item.path}
                        to={item.path || '/'}
                        className="flex items-center text-gray-600 hover:text-[#C42E0E] px-3 py-2 rounded-md text-sm transition-colors duration-200"
                        onClick={() => setIsOpen(false)}
                      >
                        <item.icon className="h-4 w-4 mr-2 text-gray-400" />
                        {item.name}
                      </Link>
                    )
                  ))}
                </div>
              )}
            </div>

            {/* Divider */}
            <div className="border-t border-gray-200 my-2"></div>

            {role === 'admin' && (
              <Link
                to="/admin/dashboard"
                className={getActiveLinkClasses('/admin', "block text-[#131D47] hover:text-[#C42E0E] px-3 py-2 rounded-md text-base font-medium transition-colors duration-200")}
                onClick={() => setIsOpen(false)}
              >
                Admin Dashboard
              </Link>
            )}

            {/* Mobile Book a Demo Button */}
            <div className="pt-4 pb-3 border-t border-gray-200">
              <Link
                to="/book-a-demo"
                className="flex items-center justify-center w-full bg-black text-white px-4 py-3 rounded-lg hover:bg-gray-800 transition-colors duration-200 group"
                onClick={() => setIsOpen(false)}
              >
                <span className="mr-2">Book a Demo</span>
                <div className="bg-white rounded p-1 group-hover:translate-x-1 transition-transform duration-200">
                  <ArrowRight className="h-4 w-4 text-black" />
                </div>
              </Link>
            </div>

            {user ? (
              <Link
                to="/profile"
                className="block text-[#131D47] hover:text-[#C42E0E] px-3 py-2 rounded-md text-base font-medium transition-colors duration-200"
                onClick={() => setIsOpen(false)}
              >
                My Account
              </Link>
            ) : (
              <div className="pt-4 pb-3 border-t border-gray-200">
                <Link
                  to="/login"
                  className="block text-[#131D47] hover:text-[#C42E0E] px-3 py-2 rounded-md text-base font-medium transition-colors duration-200"
                  onClick={() => setIsOpen(false)}
                >
                  Sign In
                </Link>
                {site_settings?.features.userRegistrationEnabled && (
                  <Link
                    to="/signup"
                    className="block w-full text-center bg-[#C42E0E] text-white px-4 py-2 rounded-md text-base font-medium hover:bg-[#a82a0c] mt-2 transition-colors duration-200"
                    onClick={() => setIsOpen(false)}
                  >
                    Sign Up
                  </Link>
                )}
              </div>
            )}
          </div>
        </div>
      )}
    </nav>
  );
}