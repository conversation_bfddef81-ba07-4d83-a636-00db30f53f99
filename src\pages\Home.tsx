import React from 'react';
import Hero from '../components/home/<USER>';
import HomeBannerSlider from '../components/home/<USER>';
import LetsConnect from '../components/home/<USER>';

import PassionLedUsHere from '../components/home/<USER>';
import ConsultingServices from '../components/home/<USER>';
import LeadershipAndPlanning from '../components/home/<USER>';
import Solutions from '../components/home/<USER>';
import ServicesOverview from '../components/home/<USER>';
import TrustedBy from '../components/grantready/TrustedBy';

const Home: React.FC = () => {

  return (
    <div className="">
      <Hero />

      {/* Home Banner Slider */}
      <HomeBannerSlider />

      <PassionLedUsHere />

      <ConsultingServices />

      {/* <OurSolutions /> */}

      <Solutions />

      <ServicesOverview />

      {/* <Capabilities /> */}

      <LeadershipAndPlanning />
      <TrustedBy
        title="Join the ranks of our trusted partners."
        description="We work with leading organizations to deliver innovative solutions."
      />
      <LetsConnect />
    </div>
  );
};

export default Home;
